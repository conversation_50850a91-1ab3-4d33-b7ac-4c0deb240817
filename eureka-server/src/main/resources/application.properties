spring.application.name=EUREKA-SERVER
server.port=8761

# Configuration du Config Server
spring.config.import=optional:configserver:http://config-server:8888

# Configuration du fail-fast et retry
spring.cloud.config.fail-fast=true
spring.cloud.config.retry.initial-interval=1000
spring.cloud.config.retry.max-attempts=6
spring.cloud.config.retry.max-interval=5000
spring.cloud.config.retry.multiplier=1.5