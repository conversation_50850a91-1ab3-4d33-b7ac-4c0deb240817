# Configuration specifique pour l'environnement Docker
spring.application.name=EUREKA-SERVER
server.port=8761

# Configuration du Config Server pour Docker
spring.config.import=optional:configserver:http://config-server:8888

# Configuration du fail-fast et retry
spring.cloud.config.fail-fast=false
spring.cloud.config.retry.initial-interval=1000
spring.cloud.config.retry.max-attempts=6
spring.cloud.config.retry.max-interval=5000
spring.cloud.config.retry.multiplier=1.5

# Configuration Eureka Server pour Docker (au cas ou config-server ne repond pas)
eureka.instance.hostname=eureka-server
eureka.instance.prefer-ip-address=false
eureka.client.register-with-eureka=false
eureka.client.fetch-registry=false
eureka.client.serviceUrl.defaultZone=http://eureka-server:8761/eureka/

# Desactiver l'auto-preservation en developpement
eureka.server.enable-self-preservation=false
eureka.server.eviction-interval-timer-in-ms=5000
