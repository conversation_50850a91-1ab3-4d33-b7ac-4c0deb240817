version: '3'

services:
  # Spring Boot Services
  config-server:
    build: ./config-server
    ports:
      - "8888:8888"
    environment:
      - SPRING_PROFILES_ACTIVE=docker,native
    networks:
      - event-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8888/actuator/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  eureka-server:
    build: ./eureka-server
    ports:
      - "8761:8761"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    depends_on:
      - config-server
    networks:
      - event-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8761/actuator/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  user-service:
    build: ./user-service
    ports:
      - "8084:8084"
    environment:
      - SERVER_PORT=8084
      - SPRING_PROFILES_ACTIVE=docker
    depends_on:
      - eureka-server
      - postgres-users
    networks:
      - event-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8084/actuator/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  event-service:
    build: ./event-service
    ports:
      - "8082:8082"
    environment:
      - SERVER_PORT=8082
      - SPRING_PROFILES_ACTIVE=docker
    depends_on:
      - user-service
      - postgres-events
    networks:
      - event-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8082/actuator/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  invitation-service:
    build: ./invitation-service
    ports:
      - "8083:8083"
    environment:
      - SERVER_PORT=8083
      - SPRING_PROFILES_ACTIVE=docker
    depends_on:
      - event-service
      - postgres-invitations
    networks:
      - event-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8083/actuator/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  notification-service:
    build: ./notification-service
    ports:
      - "8085:8085"
    environment:
      - SERVER_PORT=8085
      - SPRING_PROFILES_ACTIVE=docker
    depends_on:
      - invitation-service
      - postgres-notifications
    networks:
      - event-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8085/actuator/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  api-gateway:
    build: ./api-gateway
    ports:
      - "8093:8093"
    environment:
      - SERVER_PORT=8093
      - SPRING_PROFILES_ACTIVE=docker
    depends_on:
      - notification-service
    networks:
      - event-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8093/actuator/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Infrastructure Services
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    depends_on:
      - api-gateway
    networks:
      - event-network
    healthcheck:
      test: echo stat | nc localhost 2181
      interval: 10s
      timeout: 5s
      retries: 5

  kafka:
    image: confluentinc/cp-kafka:latest
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    networks:
      - event-network
    healthcheck:
      test: nc -z localhost 9092
      interval: 10s
      timeout: 5s
      retries: 5

  # Databases
  postgres-users:
    image: postgres:latest
    environment:
      POSTGRES_DB: users_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_users_data:/var/lib/postgresql/data
    networks:
      - event-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  postgres-events:
    image: postgres:latest
    environment:
      POSTGRES_DB: events_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5433:5432"
    volumes:
      - postgres_events_data:/var/lib/postgresql/data
    networks:
      - event-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  postgres-invitations:
    image: postgres:latest
    environment:
      POSTGRES_DB: invitations_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5434:5432"
    volumes:
      - postgres_invitations_data:/var/lib/postgresql/data
    networks:
      - event-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  postgres-notifications:
    image: postgres:latest
    environment:
      POSTGRES_DB: notifications_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5435:5432"
    volumes:
      - postgres_notifications_data:/var/lib/postgresql/data
    networks:
      - event-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Frontend
  frontend:
    build: ./frontend
    ports:
      - "4200:80"
    depends_on:
      - api-gateway
    networks:
      - event-network

networks:
  event-network:
    driver: bridge

volumes:
  postgres_users_data:
  postgres_events_data:
  postgres_invitations_data:
  postgres_notifications_data: 