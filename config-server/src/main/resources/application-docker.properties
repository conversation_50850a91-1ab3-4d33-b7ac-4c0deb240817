# Configuration specifique pour l'environnement Docker
spring.application.name=config-server
server.port=8888

# Active le profil native pour charger les configurations depuis le classpath
spring.profiles.active=native
spring.cloud.config.server.native.searchLocations=classpath:/configurations

# Desactiver Eureka pour le config-server (il doit demarrer independamment)
eureka.client.enabled=false
eureka.client.register-with-eureka=false
eureka.client.fetch-registry=false

# Logging
logging.level.org.springframework.cloud.config=DEBUG
logging.level.org.springframework.boot=DEBUG
