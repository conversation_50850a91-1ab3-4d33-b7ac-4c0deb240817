# Configuration spécifique pour l'environnement Docker
spring.application.name=config-server
server.port=8888

# Active le profil native pour charger les configurations depuis le classpath
spring.profiles.active=native
spring.cloud.config.server.native.searchLocations=classpath:/configurations

# Configuration Eureka pour Docker - utilise le nom du service
eureka.client.enabled=true
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true
eureka.client.service-url.defaultZone=http://eureka-server:8761/eureka/

# Instance configuration pour Eureka
eureka.instance.prefer-ip-address=false
eureka.instance.hostname=config-server
eureka.instance.lease-renewal-interval-in-seconds=10
eureka.instance.lease-expiration-duration-in-seconds=30

# Logging
logging.level.org.springframework.cloud.config=DEBUG
logging.level.org.springframework.boot=DEBUG
logging.level.com.netflix.eureka=DEBUG
logging.level.com.netflix.discovery=DEBUG
